#include "DisplayUtils.h"
#include "TelemetryController.h"
#include <Arduino.h>

// TODO: Replace these Serial prints with real display library calls.

namespace DisplayUtils {

// Control flag for serial printing
bool IsSerialPrintEnabled = false;

// External reference to telemetry controller
extern TelemetryController telemetry_controller;

void showWarning(const char* msg) {
   // Default implementation - use generic command context
   showWarning(CMD_WARNING, "showWarning", "DisplayUtils", msg);
}

void showWarning(uint8_t commandId, const char* methodName, const char* controllerName, const char* msg) {
   // Send to PC via telemetry with enhanced context
   telemetry_controller.showWarning(Serial, commandId, methodName, controllerName, msg);

   // Optional serial print for debugging
   if (IsSerialPrintEnabled) {
      Serial.print(F("[WARNING] "));
      Serial.println(msg);
   }
}

void showError(const char* msg) {
   // Default implementation - use generic command context
   showError(CMD_MESSAGE, "showError", "DisplayUtils", msg);
}

void showError(uint8_t commandId, const char* methodName, const char* controllerName, const char* msg) {
   // Send to PC via telemetry with enhanced context
   telemetry_controller.SendMessage(Serial, commandId, methodName, controllerName, msg);

   // Optional serial print for debugging
   if (IsSerialPrintEnabled) {
      Serial.print(F("[ERROR] "));
      Serial.println(msg);
   }
}

void showState(const char* stateCode, const char* actuatorTableJson) {
   // Default implementation - use generic command context
   showState(CMD_MESSAGE, "showState", "DisplayUtils", stateCode, actuatorTableJson);
}

void showState(uint8_t commandId, const char* methodName, const char* controllerName, const char* stateCode, const char* actuatorTableJson) {
   // Send to PC via telemetry with enhanced context
   char buffer[200];
   snprintf(buffer, sizeof(buffer), "[STATE] %s ACTS: %s", stateCode, actuatorTableJson);
   telemetry_controller.SendMessage(Serial, commandId, methodName, controllerName, buffer);

   // Optional serial print for debugging
   if (IsSerialPrintEnabled) {
      Serial.print(F("[STATE] "));
      Serial.print(stateCode);
      Serial.print(F(" ACTS: "));
      Serial.println(actuatorTableJson);
   }
}

} // namespace DISPLAYUTILS