#ifndef SAFETY_CONTROLLER_H
#define SAFETY_CONTROLLER_H

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

// Forward declarations to avoid circular dependencies
class SystemController;
class ElectrolyzerController;
class FuelCellController;

class SafetyController {
public:
      enum FaultCode : uint8_t {
            FAULT_H2_LEAK                  = 0x01,
            FAULT_LOW_WATER_LEVEL      = 0x02,
            FAULT_OVERPRESSURE           = 0x04,
            FAULT_UNDERPRESSURE         = 0x08,
            FAULT_OVERTEMP                 = 0x10,
            FAULT_UNDERTEMP               = 0x20,
            FAULT_COMMS_LOSS              = 0x40,
            FAULT_SELFTEST_FAIL         = 0x80
      };

      SafetyController();

      // Must be called periodically
      void update();

      // Subsystems report faults through this
      void reportSubsystemFault(FaultCode f);

      // Reset comm watchdog (call when telemetry arrives)
      void resetCommWatchdog();

      // Clear all faults (e.g. operator reset)
      void clearFaults();

      uint8_t getFaultStatus() const;
      bool isEmergency() const;

      void triggerEmergency();

private:
      // Internal shutdown sequence—never call this from outside
      void emergency_shutdown();

      uint8_t activeFaults;
      unsigned long lastCheckTs;
      unsigned long lastCommTs;
};

extern SafetyController safety_controller;

#endif // SAFETY_CONTROLLER_H  
