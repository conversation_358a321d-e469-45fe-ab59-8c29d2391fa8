#include <Arduino.h>
#include "TelemetryController.h"
#include "SystemController.h"
#include "SafetyController.h"
#include "Sensors.h"

// Initialize the global telemetry controller
TelemetryController telemetry_controller;


TelemetryController::TelemetryController() 
  : rxState(WAIT_HEADER1)
  , rxIndex(0)
  , expectedPayloadLength(0)
{}

void TelemetryController::processIncomingCommands(Stream &serialport) {
  while (serialport.available()) {
  uint8_t b = serialport.read();
  processByte(serialport, b);
  }
}
void TelemetryController::sendTelemetry(Stream &serialport) {
   // 1) Compute maximum buffer size:
   //   - 2 bytes: mode + fault
   //   - 1 + Nfloats*4: sensorCount + each float
   //   - 1 + 1:    diCount + digitalMask
   //   - 1 + M:    actCount + one byte per actuator
   const size_t maxBuf =
         2
      + 1 + CAL_SENSOR_COUNT * sizeof(float)
      + 1 + 1
      + 1 + ACTUATOR_PIN_COUNT;
   uint8_t telemetryBuffer[maxBuf];
   size_t idx = 0;

   // --- 1) Mode + fault status
   telemetryBuffer[idx++] = uint8_t(system_controller.get_mode());
   telemetryBuffer[idx++] = uint8_t(safety_controller.getFaultStatus());

   // --- 2) Sensor values
   telemetryBuffer[idx++] = CAL_SENSOR_COUNT;                        // sensorCount
   for (uint8_t i = 0; i < CAL_SENSOR_COUNT; ++i) {
      float v = getFilteredCalibratedValueADS(i, i);
      memcpy(&telemetryBuffer[idx], &v, sizeof(v));
      idx += sizeof(v);
   }

   // --- 3) Digital inputs
   telemetryBuffer[idx++] = DIGITAL_INPUT_COUNT;                   // diCount *corrected*
   uint8_t digitalMask = 0;
   if (GetDigitalInputVal(PIN_ELEC_O2_DRYER_LEVEL_LS_02)    == HIGH) digitalMask |= 0x01;
   if (GetDigitalInputVal(PIN_ELEC_H2_DRYER_LEVEL_LS_03)    == HIGH) digitalMask |= 0x02;
   if (GetDigitalInputVal(PIN_FC_O2_DRYER_LEVEL_LS_04)      == HIGH) digitalMask |= 0x04;
   if (GetDigitalInputVal(PIN_SENSOR_FIRE_DETECT_FS_01)     == HIGH) digitalMask |= 0x08;
   if (GetDigitalInputVal(PIN_SENSOR_H2_AMBIENT_HS_01)      == HIGH) digitalMask |= 0x10;
   if (GetDigitalInputVal(PIN_EMERGENCY_STOP_ES_01)         == HIGH) digitalMask |= 0x20;
   telemetryBuffer[idx++] = digitalMask;                           // digitalMask

   // --- 4) Actuator states
   telemetryBuffer[idx++] = ACTUATOR_PIN_COUNT;                   // actCount *NEW*: number of states
   // Now append each one byte state in the *same order* your C# expects:
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_WATER_PUMP_RELAY_PM_02)                     == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_WATER_INLET_VALVE_SV_01)                    == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_ELEC_HEATER_EH_01)                          == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_H2_OUTPUT_VALVE_SV_03)                      == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_O2_OUTPUT_VALVE_SV_02)                      == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04)             == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_EL_O2_CHILLER_RELAY_CF_01)                  == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_WATER_FILL_PUMP_PM_01) == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_FC_H2_SUPPLY_VALVE_SV_05)                   == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_FC_O2_SUPPLY_VALVE_SV_06)                   == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_FC_O2_FAN_RF_01)                            == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_FC_H2_DISCHARGE_VALVE_SV_07)                == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_FC_O2_CHILLER_RELAY_CF_02)                  == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_FC_O2_DRYER_PUMP_PM_03)                     == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_FC_LOAD_RELAY)                        == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_BMS_CHARGE_RELAY)                     == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_BMS_DISCHARGE_RELAY)                  == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_INVERTER_RELAY)                       == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_EMERGENCY_VENT)                       == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_MAIN_INPUT_RELAY)                     == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_MAIN_BMS_OUT_RELAY)                   == HIGH);
   telemetryBuffer[idx++] = (GetDigitalInputVal(ACT_ELECTROLIZER_PSU_RELAY)               == HIGH);

   // --- 5) Final frame & send
   sendPacket(serialport, CMD_TELEMETRY_DATA, telemetryBuffer, idx);
}



/*
void TelemetryController::sendAck(Stream &serialport, uint8_t origCommand, bool success, const char* message) {
  uint8_t payload[130];
  uint8_t msgLen = strlen(message);
  payload[0] = origCommand;
  payload[1] = success ? 1 : 0;
  memcpy(&payload[2], message, msgLen);
  sendPacket(serialport, CMD_ACKNOWLEDGMENT, payload, msgLen + 2);
}
*/



void TelemetryController::sendAck(Stream &serialport, uint8_t origCommand, bool success, const char* message,
  const ActuatorAckData* actuatorData /* = nullptr */)
{
   uint8_t payload[130];
   uint8_t msgLen = strlen(message);
   uint8_t idx = 0;

   payload[idx++] = origCommand; // data[0]: original command
   payload[idx++] = success ? 1 : 0; // data[1]: success

   // If actuatorData is provided, append extra fields
   if (actuatorData != nullptr) {
    payload[idx++] = actuatorData->pin; // data[2]: pin
    payload[idx++] = actuatorData->commandId; // data[3]: command
    payload[idx++] = actuatorData->timestamp; // data[4]: timestamp
   }

   // Append message string after (always safe: payload[] is large)
   memcpy(&payload[idx], message, msgLen);
   idx += msgLen;

   sendPacket(serialport, CMD_ACKNOWLEDGMENT, payload, idx);
}

void TelemetryController::sendMessage(Stream &serialport, const char* message) {
   uint8_t payload[130];
   uint8_t msgLen = strlen(message);

   // Simple message payload - just the message text
   memcpy(payload, message, msgLen);

   sendPacket(serialport, CMD_MESSAGE, payload, msgLen);
}

void TelemetryController::SendMessage(Stream &serialport, uint8_t commandId, const char* methodName, const char* controllerName, const char* message) {
   uint8_t payload[130];
   uint8_t idx = 0;

   // Payload structure:
   // [0]: Command ID
   // [1]: Method name length
   // [2..n]: Method name
   // [n+1]: Controller name length
   // [n+2..m]: Controller name
   // [m+1]: Message length
   // [m+2..end]: Message

   uint8_t methodLen = strlen(methodName);
   uint8_t controllerLen = strlen(controllerName);
   uint8_t msgLen = strlen(message);

   // Check if payload fits in buffer
   if (1 + 1 + methodLen + 1 + controllerLen + 1 + msgLen > 130) {
      // Truncate message if needed
      msgLen = 130 - (1 + 1 + methodLen + 1 + controllerLen + 1);
   }

   payload[idx++] = commandId;
   payload[idx++] = methodLen;
   memcpy(&payload[idx], methodName, methodLen);
   idx += methodLen;
   payload[idx++] = controllerLen;
   memcpy(&payload[idx], controllerName, controllerLen);
   idx += controllerLen;
   payload[idx++] = msgLen;
   memcpy(&payload[idx], message, msgLen);
   idx += msgLen;

   sendPacket(serialport, CMD_MESSAGE, payload, idx);
}

void TelemetryController::showWarning(Stream &serialport, uint8_t commandId, const char* methodName, const char* controllerName, const char* message) {
   uint8_t payload[130];
   uint8_t idx = 0;

   // Same payload structure as SendMessage but uses CMD_WARNING
   uint8_t methodLen = strlen(methodName);
   uint8_t controllerLen = strlen(controllerName);
   uint8_t msgLen = strlen(message);

   // Check if payload fits in buffer
   if (1 + 1 + methodLen + 1 + controllerLen + 1 + msgLen > 130) {
      // Truncate message if needed
      msgLen = 130 - (1 + 1 + methodLen + 1 + controllerLen + 1);
   }

   payload[idx++] = commandId;
   payload[idx++] = methodLen;
   memcpy(&payload[idx], methodName, methodLen);
   idx += methodLen;
   payload[idx++] = controllerLen;
   memcpy(&payload[idx], controllerName, controllerLen);
   idx += controllerLen;
   payload[idx++] = msgLen;
   memcpy(&payload[idx], message, msgLen);
   idx += msgLen;

   sendPacket(serialport, CMD_WARNING, payload, idx);
}

void TelemetryController::sendLogMessage(Stream &serial, LogLevel level, const char* message) {
  const size_t maxChunk = 129; // 1 byte for level + up to 129 bytes of text = 130 payload
  size_t msgLen = strlen(message);
  size_t offset = 0;
  while (offset < msgLen) {
  size_t chunkLen = min(maxChunk, msgLen - offset);
  uint8_t payload[130];
  payload[0] = (uint8_t) level;
  memcpy(&payload[1], message + offset, chunkLen);
  //Serial1: DEBUG_SERIAL
  sendPacket(serial, CMD_LOG_MESSAGE, payload, chunkLen + 1);  
  offset += chunkLen;
  }
}

void TelemetryController::sendPacket(Stream &serialport, uint8_t command, const uint8_t* data, uint8_t dataLength) {
  uint8_t packet[256];
  uint8_t idx = 0;
  packet[idx++] = 0xAA;
  packet[idx++] = 0x55;
  packet[idx++] = dataLength;
  packet[idx++] = command;
  for (uint8_t i = 0; i < dataLength; i++) {
  packet[idx++] = data[i];
  }
  uint16_t crc = calculateCRC(&packet[2], dataLength + 2);
  packet[idx++] = highByte(crc);
  packet[idx++] = lowByte(crc);
  serialport.write(packet, idx);
}

void TelemetryController::processByte(Stream &serialport, uint8_t b) {
  switch (rxState) {
  case WAIT_HEADER1:
    if (b == 0xAA) rxState = WAIT_HEADER2;
    break;
  case WAIT_HEADER2:
    if (b == 0x55) rxState = WAIT_LENGTH;
    else rxState = WAIT_HEADER1;
    break;
  case WAIT_LENGTH:
    incomingDataLength = b;
    expectedPayloadLength = b + 3;
    rxIndex = 0;
    rxState = WAIT_PAYLOAD;
    break;
  case WAIT_PAYLOAD:
    if (rxIndex < sizeof(rxBuffer)) rxBuffer[rxIndex++] = b;
    if (rxIndex >= expectedPayloadLength) {
    if (expectedPayloadLength < 3) {
      sendAck(serialport, 0, false, "Invalid packet length");
      rxState = WAIT_HEADER1;
      break;
    }
    uint8_t dataLen = incomingDataLength;
    uint16_t receivedCrc = (rxBuffer[dataLen + 1] << 8) | rxBuffer[dataLen + 2];
    uint8_t crcBuffer[256];
    crcBuffer[0] = incomingDataLength;
    crcBuffer[1] = rxBuffer[0];
    for (uint8_t i = 0; i < dataLen; i++) {
      crcBuffer[2 + i] = rxBuffer[1 + i];
    }
    uint16_t calculatedCrc = calculateCRC(crcBuffer, dataLen + 2);
    if (calculatedCrc != receivedCrc) {
      sendAck(serialport, rxBuffer[0], false, "CRC error");
      rxState = WAIT_HEADER1;
      break;
    }
    ControlCommand cmd;
    cmd.command = rxBuffer[0];
    cmd.dataLength = dataLen;
    for (uint8_t i = 0; i < dataLen; i++) {
      cmd.data[i] = rxBuffer[1 + i];
    }
    processCommand(serialport, cmd);
    rxState = WAIT_HEADER1;
    }
    break;
  }
}

void TelemetryController::processCommand(Stream &serialport, const ControlCommand &cmd) {
  switch (cmd.command) {
  case CMD_MODE_CHANGE_REQUEST:
    if (cmd.dataLength < 1)
    sendAck(serialport, cmd.command, false, "Missing mode parameter");
    else {
    uint8_t newMode = cmd.data[0];
    system_controller.request_mode((SystemController::OperationMode)newMode);
    sendAck(serialport, cmd.command, true, "Mode change requested");
    }
    break;
  case CMD_CALIBRATION_GET:
    sendPacket(serialport, CMD_CALIBRATION_GET, (uint8_t*)&calibration_data, sizeof(CalibrationData));
    sendAck(serialport, cmd.command, true, "Calibration data sent");
    break;
  case CMD_CALIBRATION_UPDATE:
    if (cmd.dataLength != sizeof(CalibrationData))
    sendAck(serialport, cmd.command, false, "Invalid calibration data size");
    else {
    memcpy(&calibration_data, cmd.data, sizeof(CalibrationData));
    if (calibration_data.magic != CALIBRATION_MAGIC_VALUE)
      sendAck(serialport, cmd.command, false, "Invalid calibration magic");
    else {
      saveCalibrationToEEPROM();
      sendAck(serialport, cmd.command, true, "Calibration updated");
    }
    }
    break;
  case CMD_TELEMETRY_REQUEST:
    sendTelemetry(serialport);
    sendAck(serialport, cmd.command, true, "Telemetry sent");
    break;
  case CMD_SIMULATION_UPDATE:
    if (cmd.dataLength != SIM_UPDATE_PAYLOAD_LENGTH)
    sendAck(serialport, cmd.command, false, "Invalid simulation update data length");
    else {
    simulationMode = (cmd.data[0] != 0);
    memcpy(analogSensors, &cmd.data[1], sizeof(float) * NUM_ANALOG_SENSORS);
    memcpy(adsSensors, &cmd.data[1 + 64], sizeof(float) * 14);
    for (int i = 0; i < NUM_PZEMS; i++) {//EL,FC INV,INPUT
      int offset = 1 + 64 + 64 + i * 24;
      memcpy(&pzemModel[i], &cmd.data[offset], sizeof(PZEMModel));
    }
    memcpy(&simDigitalOutputs, &cmd.data[1 + 64 + 64 + 72], sizeof(simDigitalOutputs));
    sendAck(serialport, cmd.command, true, "Simulation update applied");
    }
    break;
  case CMD_SET_ACTUATOR:
    if (cmd.dataLength < 2) {
    // Serial1.println(F("Missing actuator index or value..."));
    sendAck(serialport, cmd.command, false, "Missing actuator index or value");
    break;
    }
    {
    uint8_t index = cmd.data[0];
			
    if (index >= ACTUATOR_PIN_END || index < ACTUATOR_PIN_START) {
      sendAck(serialport, cmd.command, false, "Invalid actuator index");
      break;
    }
    
    uint8_t value = cmd.data[1];

    if(IsPowerRelayPin(index))
      value = !cmd.data[1];//PROCESS INVERTED, LOW==HIGH

    // uint8_t pin = index;
    pinMode(index, OUTPUT); // Ensure pin is output

    if (value)
      digitalWrite(index, HIGH);
    else
      digitalWrite(index, LOW);

    // Optionally confirm state:
    bool actual = (digitalRead(index) == HIGH);
    char msg[48];
    snprintf(msg, sizeof(msg), "Actuator %d set to %s", index, actual ? "HIGH" : "LOW");

    ActuatorAckData actData;
    actData.success = 1;
    actData.pin = index;
    actData.commandId = cmd.command;
    actData.timestamp = (millis() >> 2) & 0xFF;

    sendAck(serialport, cmd.command, true, msg, &actData);
  }
  break;

  case CMD_SET_EL_PSU: {
    // Serial1.println(F("CMD_SET_EL_PSU..."));
    if (cmd.dataLength < 4) {
      // Serial1.println(F("Electrolyzer Voltage Payload too short..."));
      sendAck(serialport, cmd.command, false, "Electrolyzer Voltage Payload too short");
      break;
    }
    float volt;
    memcpy(&volt, cmd.data, sizeof(float));

    // Clamp voltage
    if (volt < 0.0f) volt = 0.0f;
    if (volt > 15.0f) volt = 15.0f;

    elRegulator.setOutputVoltage(volt);
    // Serial1.println(F("Electrolyzer Voltage set..."));
    sendAck(serialport, cmd.command, true, "Electrolyzer voltage set");
    break;
  }


  default:
    sendAck(serialport, cmd.command, false, "Unknown command");
    break;
  }
}
