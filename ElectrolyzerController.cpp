#include "ElectrolyzerController.h"
#include "DisplayUtils.h"
#include "Controllers.h"
#include "Sensors.h"
#include "SafetyController.h"

// Global ElectrolyzerController instance
ElectrolyzerController electrolyzer_controller;

ElectrolyzerController::ElectrolyzerController()
   : _state(IDLE), _tState(0), _tLastSwitch(0),
      _tHeaterOn(0), _heaterRunning(false), _seqStep(0)
{}

void ElectrolyzerController::begin() {
   reset();
}

void ElectrolyzerController::reset() {
   _state = PRS1_WATER_CHECK;
   _tState = millis();
   _tLastSwitch= millis();
   _heaterRunning = false;
   _seqStep = 0;
   DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "reset", "ElectrolyzerController", stateToString(_state), actuatorStatusJson());
}

void ElectrolyzerController::update() {
   // emergency override
   if (_state != EMERGENCY && safety_controller.getFaultStatus() != 0) {
      _state = EMERGENCY;
      _tState = millis();
      performPRS3(); // immediately cut
      return;
   }

   // always handle LS-03 waste-tank auto-drain
   handleWasteTank();

   switch (_state) {
      case PRS1_WATER_CHECK:
      case PRS1_POWER_CHECK:
      case PRS1_GAS_CHECK:
         performPRS1();
         break;
      case PRS2_RUN:
         performPRS2();
         break;
      case PRS3_SHUTDOWN_DELAY:
      case PRS3_SHUTDOWN_OFF:
         performPRS3();
         break;
      case EMERGENCY:
         emergency_stop();
         break;
      default:
         break;
   }
}

void ElectrolyzerController::readAllSensors() {
   _lvl_LS01 = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_LEVEL_LS_01, CAL_WATER_LEVEL); // LS-01: Main water tank (Ana su tankı)
   _lvl_LS02 = GetDigitalInputVal(PIN_ELEC_O2_DRYER_LEVEL_LS_02) ? 100.f : 0.f;              // LS-02: Separator-01 (Separatör-01)
   _lvl_LS03 = GetDigitalInputVal(PIN_ELEC_H2_DRYER_LEVEL_LS_03) ? 100.f : 0.f;              // LS-03: Waste water tank (Atık su tankı)
   _press_PT01 = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_PRESS_PT_01, CAL_WATER_PRESS);
   _press_PT02 = getFilteredCalibratedValueADS(ADC_SENSOR_H2_TANK_PRESS_PT_02, CAL_H2_TANK_PRESS);
   _press_PT03 = getFilteredCalibratedValueADS(ADC_SENSOR_O2_TANK_PRESS_PT_03, CAL_O2_TANK_PRESS);
   _temp_TT01 = getFilteredCalibratedValueADS(ADC_SENSOR_WATER_TEMP_TT_01, CAL_WATER_TEMP);
   _temp_TT02 = getFilteredCalibratedValueADS(ADC_SENSOR_ELEC_TEMP_TT_02, CAL_ELEC_TEMP);
   _volt_EL = getVolt_EL();
   _curr_EL = getACSCurrent_EL();
}

void ElectrolyzerController::performPRS1() {
   readAllSensors();
   unsigned long now = millis();

   switch (_state) {
      case PRS1_WATER_CHECK:
         // PRS-1: Check water level in LS-02 (Separator-01) per procedure 3.1.1
         if (_lvl_LS02 >= Constants::WATER_LEVEL_MIN) {
            _state = PRS1_POWER_CHECK;
            _tState = now;
            DisplayUtils::showState(stateToString(_state), actuatorStatusJson());
         } else {
            // Check LS-01 (Main water tank) and pump water to LS-02 (Separator-01)
            if (_lvl_LS01 >= Constants::WATER_LEVEL_MIN &&
                  now - _tLastSwitch >= Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL) {
               SetDigitalOutputVal(ACT_WATER_FILL_PUMP_PM_01, HIGH);
               _tLastSwitch = now;
            }
            if (_lvl_LS02 >= Constants::WATER_LEVEL_MIN || _lvl_LS01 < Constants::WATER_LEVEL_MIN) {
               SetDigitalOutputVal(ACT_WATER_FILL_PUMP_PM_01, LOW);
               if (_lvl_LS01 < Constants::WATER_LEVEL_MIN) {
                  safety_controller.reportSubsystemFault(SafetyController::FAULT_LOW_WATER_LEVEL);
                  DisplayUtils::showError("Su yok! Ana su tankina su ekleyin.");
               }
               _state = PRS1_POWER_CHECK;
               _tState = now;
               DisplayUtils::showState(stateToString(_state), actuatorStatusJson());
            }
         }
         break;

      case PRS1_POWER_CHECK:
         if (!Constants::IsVoltageCheckEnabled ||
             (_volt_EL >= Constants::MIN_OPERATING_VOLTAGE &&
              _volt_EL <= Constants::MAX_OPERATING_VOLTAGE &&
              _curr_EL >= (Constants::MIN_OPERATING_VOLTAGE/10.0f))) {
            _state = PRS1_GAS_CHECK;
            _tState = now;
            DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "performPRS1", "ElectrolyzerController", stateToString(_state), actuatorStatusJson());
         } else {
            DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS1", "ElectrolyzerController", "Sistem guc degerleri uygun degildir!");
            safety_controller.reportSubsystemFault(SafetyController::FAULT_SELFTEST_FAIL);
         }
         break;

      case PRS1_GAS_CHECK:
         if (_press_PT02 <= Constants::MAX_TANK_PRESSURE_BAR &&
               _press_PT03 <= Constants::MAX_TANK_PRESSURE_BAR) {
            SetDigitalOutputVal(ACT_ELECTROLIZER_PSU_RELAY, HIGH);
            _state = PRS2_RUN;
            _tState = now;
            _seqStep = 0;
            _tLastSwitch = now;
            DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "performPRS1", "ElectrolyzerController", stateToString(_state), actuatorStatusJson());
         } else {
            // Tank pressure too high - cannot start electrolyzer per PRS-1
            if (_press_PT02 > Constants::MAX_TANK_PRESSURE_BAR) {
               DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS1", "ElectrolyzerController", "Hidrojen tanki dolu, Elektrolizor calistirilamaz!");
            }
            if (_press_PT03 > Constants::MAX_TANK_PRESSURE_BAR) {
               DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS1", "ElectrolyzerController", "Oksijen tanki dolu, Elektrolizor calistirilamaz!");
            }
            safety_controller.reportSubsystemFault(SafetyController::FAULT_OVERPRESSURE);
         }
         break;

      default:
         break;
   }
}

void ElectrolyzerController::performPRS2() {
   readAllSensors();
   unsigned long now = millis();
   if (now - _tLastSwitch < Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL) return;

   // if waste dryer full → trigger shutdown
   if (_lvl_LS03 >= Constants::WATER_LEVEL_MIN) {
      _state = PRS3_SHUTDOWN_DELAY;
      _tState = now;
      DisplayUtils::showError(CMD_MODE_CHANGE_REQUEST, "performPRS2", "ElectrolyzerController", "Atik su tanki dolu, kapatiyorum.");
      return;
   }

   // PRS-2: Electrolyzer startup sequence per procedure 4.1.1
   switch (_seqStep) {
      case 0:
         // Step 1: Open valves SV-01, SV-02, SV-03
         SetDigitalOutputVal(ACT_WATER_INLET_VALVE_SV_01, HIGH);
         break;
      case 1:
         SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE_SV_02, HIGH);
         break;
      case 2:
         SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE_SV_03, HIGH);
         break;
      case 3:
         // Step 2: Start water pump PM-02
         SetDigitalOutputVal(ACT_WATER_PUMP_RELAY_PM_02, HIGH);
         break;
      case 4:
         // Step 3: Start chillers CF-01, CF-02
         SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY_CF_01, HIGH);
         SetDigitalOutputVal(ACT_FC_O2_CHILLER_RELAY_CF_02, HIGH);
         break;
      case 5:
         // Step 4: Optional heater EH-01 (auto if TT-01 < 20°C, max 1 min runtime)
         if (Constants::IsHeaterEnabled && _temp_TT01 < Constants::PRODUCTION_START_TEMP) {
            SetDigitalOutputVal(ACT_ELEC_HEATER_EH_01, HIGH);
            _heaterRunning = true;
            _tHeaterOn = now;
         }
         break;
      case 6:
         // Start electrolyzer power supply
         elRegulator.setOutputVoltage(Constants::WATER_PRESSURE_START * 2);
         break;
      default:
         // PRS-2: Monitor sensor limits during operation per procedure 4.1.2
         // PT-01: ≤4 barg, PT-02: ≤4 barg, PT-03: ≤4 barg
         // TT-01: 15-55°C, TT-02: 15-55°C
         // LS-03: Waste tank full triggers shutdown per procedure
         if (_press_PT01 > Constants::WATER_PRESSURE_STOP ||
               _press_PT02 > Constants::MAX_TANK_PRESSURE_BAR ||
               _press_PT03 > Constants::MAX_TANK_PRESSURE_BAR ||
               _temp_TT02 > Constants::ELEC_TEMP_HIGH ||
               _temp_TT02 < Constants::ELEC_TEMP_LOW ||
               _temp_TT01 > Constants::WATER_TEMP_HIGH ||
               _temp_TT01 < Constants::WATER_TEMP_LOW ||
               _lvl_LS03 >= Constants::WATER_LEVEL_MIN) {
            safety_controller.reportSubsystemFault(SafetyController::FAULT_OVERTEMP);
            _state = PRS3_SHUTDOWN_DELAY;
            _tState = now;
            if (_lvl_LS03 >= Constants::WATER_LEVEL_MIN) {
               DisplayUtils::showError("Atik su tanki dolu, kapatiyorum.");
            } else {
               DisplayUtils::showError("Limit asimi, kapatiyorum.");
            }
         }
         // Heater control per procedure: maintain 20-55°C, max 1 minute runtime
         if (_heaterRunning) {
            // Turn off heater if: 1) max runtime exceeded, 2) temperature reached target
            if (now - _tHeaterOn >= Constants::HEATER_MAX_RUNTIME ||
                _temp_TT01 >= Constants::WATER_TEMP_HIGH) {
               SetDigitalOutputVal(ACT_ELEC_HEATER_EH_01, LOW);
               _heaterRunning = false;
            }
         }
         // Turn on heater if temperature drops below 20°C and not already running
         else if (Constants::IsHeaterEnabled && _temp_TT01 < Constants::PRODUCTION_START_TEMP) {
            SetDigitalOutputVal(ACT_ELEC_HEATER_EH_01, HIGH);
            _heaterRunning = true;
            _tHeaterOn = now;
         }
         return;
   }

   _seqStep++;
   _tLastSwitch = now;
}

void ElectrolyzerController::performPRS3() {
   unsigned long now = millis();

   // 4.2.1 cut bus immediately
   if (_state == PRS3_SHUTDOWN_DELAY) {
      cutBusPower();
      if (now - _tState >= 5000) {
         _state = PRS3_SHUTDOWN_OFF;
         _tState = now;
         DisplayUtils::showState(stateToString(_state), actuatorStatusJson());
      }
      return;
   }

   // 4.2.2 close in exact doc order
   unsigned long dt = now - _tState;
   if (dt < Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL) return;

   if (dt < Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL * 1) {
      // 1) PM-02 & EH-01
      SetDigitalOutputVal(ACT_WATER_PUMP_RELAY_PM_02, LOW);
      SetDigitalOutputVal(ACT_ELEC_HEATER_EH_01, LOW);
   }
   else if (dt < Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL * 2) {
      // 2) SV-01, SV-02, SV-03
      SetDigitalOutputVal(ACT_WATER_INLET_VALVE_SV_01, LOW);
      SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE_SV_02, LOW);
      SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE_SV_03, LOW);
   }
   else if (dt < Constants::ELECTROLYZER_MIN_SWITCH_INTERVAL * 3) {
      // 3) CF-01, CF-02
      SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY_CF_01, LOW);
      SetDigitalOutputVal(ACT_FC_O2_CHILLER_RELAY_CF_02, LOW);
   }
   else {
      // final: PSU off
      SetDigitalOutputVal(ACT_ELECTROLIZER_PSU_RELAY, LOW);
      _state = STOPPED;
      DisplayUtils::showState(CMD_MODE_CHANGE_REQUEST, "performPRS3", "ElectrolyzerController", stateToString(_state), actuatorStatusJson());
   }
}

void ElectrolyzerController::handleWasteTank() {
   static unsigned long tOpenStart = 0;
   static bool sv04open = false;

   // LS-03: Waste water tank level sensor per procedure
   // When full, show safety warning and open SV-04 for 20 seconds
   if (_lvl_LS03 >= Constants::WATER_LEVEL_MIN && !sv04open) {
      SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, HIGH);
      tOpenStart = millis();
      sv04open = true;
      DisplayUtils::showWarning(CMD_MODE_CHANGE_REQUEST, "handleWasteTank", "ElectrolyzerController",
         "Su tahliyesi esnasinda hatta basinc olacagindan dikkatli ve yavas aciniz."
      );
      logMessage(LOG_INFO, F("LS-03 waste tank full - SV-04 discharge started"));
   }
   if (sv04open && millis() - tOpenStart >= Constants::H2_DRYER_DISCHARGE_TIME) {
      SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, LOW);
      sv04open = false;
      logMessage(LOG_INFO, F("LS-03 waste tank discharge completed"));
   }
}

void ElectrolyzerController::cutBusPower() {
   SetDigitalOutputVal(ACT_ELECTROLIZER_PSU_RELAY, LOW);
}

void ElectrolyzerController::emergency_stop() {
   // immediate all-off
   SetDigitalOutputVal(ACT_WATER_INLET_VALVE_SV_01, LOW);
   SetDigitalOutputVal(ACT_O2_OUTPUT_VALVE_SV_02, LOW);
   SetDigitalOutputVal(ACT_H2_OUTPUT_VALVE_SV_03, LOW);
   SetDigitalOutputVal(ACT_WATER_PUMP_RELAY_PM_02, LOW);
   SetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY_CF_01, LOW);
   SetDigitalOutputVal(ACT_FC_O2_CHILLER_RELAY_CF_02, LOW);
   SetDigitalOutputVal(ACT_ELEC_HEATER_EH_01, LOW);
   SetDigitalOutputVal(ACT_ELECTROLIZER_PSU_RELAY, LOW);
   SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04,LOW);
   // additionally cut mains & BMS
   SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);
   SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, LOW);

   _state = EMERGENCY;
   DisplayUtils::showError("EMERGENCY STOP!");
}

const char* ElectrolyzerController::stateToString(State s) const {
   switch(s) {
      case PRS1_WATER_CHECK: return "PRS1_SU_KONTROL";
      case PRS1_POWER_CHECK: return "PRS1_GUC_KONTROL";
      case PRS1_GAS_CHECK: return "PRS1_GAZ_KONTROL";
      case PRS2_RUN: return "PRS2_CALISMA";
      case PRS3_SHUTDOWN_DELAY: return "PRS3_BEKLEME";
      case PRS3_SHUTDOWN_OFF: return "PRS3_KAPATMA";
      case STOPPED: return "DURDURULDU";
      case EMERGENCY: return "ACIL_DURDURMA";
      case IDLE:
      default: return "BOS";
   }
}

const char* ElectrolyzerController::actuatorStatusJson() const {
   // Build small JSON table of each ACT_* pin state
   static char buf[256];
   snprintf(buf, sizeof(buf),
      "{\"SV01\":%d,\"SV02\":%d,\"SV03\":%d,\"PM02\":%d,\"CF01\":%d,\"CF02\":%d,\"EH01\":%d}",
      GetDigitalOutputVal(ACT_WATER_INLET_VALVE_SV_01),
      GetDigitalOutputVal(ACT_O2_OUTPUT_VALVE_SV_02),
      GetDigitalOutputVal(ACT_H2_OUTPUT_VALVE_SV_03),
      GetDigitalOutputVal(ACT_WATER_PUMP_RELAY_PM_02),
      GetDigitalOutputVal(ACT_EL_O2_CHILLER_RELAY_CF_01),
      GetDigitalOutputVal(ACT_FC_O2_CHILLER_RELAY_CF_02),
      GetDigitalOutputVal(ACT_ELEC_HEATER_EH_01)
   );
   return buf;
}