#ifndef ELECTROLYZER_CONTROLLER_H
#define ELECTROLYZER_CONTROLLER_H

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

class ElectrolyzerController {
public:
   enum State {
      IDLE,
      PRS1_WATER_CHECK,
      PRS1_POWER_CHECK,
      PRS1_GAS_CHECK,
      PRS2_RUN,
      PRS3_SHUTDOWN_DELAY,
      PRS3_SHUTDOWN_OFF,
      STOPPED,
      EMERGENCY
   };

   ElectrolyzerController();
   void begin();
   void update();
   void reset();
   void emergency_stop();

   State getState() const { return _state; }

private:
   // sensor readings
   float _lvl_LS01, _lvl_LS02, _lvl_LS03;
   float _press_PT01, _press_PT02, _press_PT03;
   float _temp_TT01, _temp_TT02;
   float _volt_EL, _curr_EL;

   State _state;
   unsigned long _tState;
   unsigned long _tLastSwitch;
   unsigned long _tHeaterOn;
   bool _heaterRunning;
   int _seqStep;

   void readAllSensors();
   void performPRS1();
   void performPRS2();
   void performPRS3();
   void handleWasteTank();
   void cutBusPower();

   const char* stateToString(State s) const;
   const char* actuatorStatusJson() const;
};

extern ElectrolyzerController electrolyzer_controller;

#endif // ELECTROLYZER_CONTROLLER_H