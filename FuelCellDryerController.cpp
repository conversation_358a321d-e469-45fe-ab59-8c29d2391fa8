#include "FuelCellDryerController.h"

// Global FuelCellDryerController instance
FuelCellDryerController fuelcell_dryer_controller;

FuelCellDryerController::FuelCellDryerController()
    : sensorFcO2(PIN_FC_O2_DRYER_LEVEL_LS_04),
    fcO2DryerActive(false),
    lastFcO2DryerToggle(0)
{}

void FuelCellDryerController::begin() {
  pinMode(PIN_FC_O2_DRYER_LEVEL_LS_04, INPUT_PULLUP);
}

void FuelCellDryerController::update() {
  unsigned long now = millis();  
  
  // Fuel Cell O2 Dryer
  if (GetDigitalInputVal(sensorFcO2) == HIGH) {
    // Dryer level high (needs discharge)
    if (!fcO2DryerActive && (now - lastFcO2DryerToggle > Constants::DRYER_MIN_TOGGLE_INTERVAL)) {
      fcO2DryerActive = true;
      SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP_PM_03, HIGH);
      lastFcO2DryerToggle = now;
      logMessage(LOG_INFO, F("FuelCellDryerController: Fuel Cell O2 dryer discharge started."));
    }
  } else {
    // Dryer level low (stop discharge)
    if (fcO2DryerActive && (now - lastFcO2DryerToggle > Constants::DRYER_MIN_TOGGLE_INTERVAL)) {
      fcO2DryerActive = false;
      SetDigitalOutputVal(ACT_FC_O2_DRYER_PUMP_PM_03, LOW);
      lastFcO2DryerToggle = now;
      logMessage(LOG_INFO, F("FuelCellDryerController: Fuel Cell O2 dryer discharge stopped."));
    }
  }
  
}
