#ifndef DISPLAYUTILS_H
#define DISPLAYUTILS_H

#include <Arduino.h>

/**
  * DisplayUtils.h
  *
  * Provides simple functions to show messages and state on
  * the operator display. Implement these to hook into your
  * actual LCD/OLED/tft library calls.
  */
namespace DisplayUtils {
   // Control flag for serial printing
   extern bool IsSerialPrintEnabled;

   /**
     * Show a warning message (e.g. yellow/red popup).
     * @param msg Null-terminated UTF-8 string to display.
     */
   void showWarning(const char* msg);

   /**
     * Show a warning message with command context.
     * @param commandId The command enum ID that triggered this warning
     * @param methodName The name of the method sending the warning
     * @param controllerName The name of the controller sending the warning
     * @param msg Null-terminated UTF-8 string to display.
     */
   void showWarning(uint8_t commandId, const char* methodName, const char* controllerName, const char* msg);

   /**
     * Show an error message (e.g. blocking red alert).
     * @param msg Null-terminated UTF-8 string to display.
     */
   void showError(const char* msg);

   /**
     * Show an error message with command context.
     * @param commandId The command enum ID that triggered this error
     * @param methodName The name of the method sending the error
     * @param controllerName The name of the controller sending the error
     * @param msg Null-terminated UTF-8 string to display.
     */
   void showError(uint8_t commandId, const char* methodName, const char* controllerName, const char* msg);

   /**
     * Show the current system state and actuator status table.
     * @param stateCode Short code string (e.g. "PRS1_SU_KONTROL")
     * @param actuatorTableJson JSON string of each actuator pin state.
     */
   void showState(const char* stateCode, const char* actuatorTableJson);

   /**
     * Show the current system state and actuator status table with command context.
     * @param commandId The command enum ID that triggered this state change
     * @param methodName The name of the method sending the state
     * @param controllerName The name of the controller sending the state
     * @param stateCode Short code string (e.g. "PRS1_SU_KONTROL")
     * @param actuatorTableJson JSON string of each actuator pin state.
     */
   void showState(uint8_t commandId, const char* methodName, const char* controllerName, const char* stateCode, const char* actuatorTableJson);
}

#endif // DISPLAYUTILS_H