#include "ElectrolyzerDryerController.h"
#include "DisplayUtils.h"

// Global ElectrolyzerDryerController instance
ElectrolyzerDryerController electrolyzer_dryer_controller;

ElectrolyzerDryerController::ElectrolyzerDryerController()
   : sensorO2Dryer(PIN_ELEC_O2_DRYER_LEVEL_LS_02),
      sensorH2Dryer(PIN_ELEC_H2_DRYER_LEVEL_LS_03),
      h2DrainOpen(false),
      drainStartTs(0)
{}

void ElectrolyzerDryerController::begin() {
   pinMode(sensorO2Dryer, INPUT_PULLUP);
   pinMode(sensorH2Dryer, INPUT_PULLUP);
}

void ElectrolyzerDryerController::update() {
   unsigned long now = millis();

   // H2 dryer: when level full → open SV-04 for exactly 20 s
   bool h2Full = (GetDigitalInputVal(sensorH2Dryer) == HIGH);
   if (h2Full && !h2DrainOpen) {
      h2DrainOpen = true;
      drainStartTs = now;
      SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, HIGH);
      logMessage(LOG_INFO, F("H2 dryer discharge started."));
      DisplayUtils::showWarning(CMD_MODE_CHANGE_REQUEST, "update", "ElectrolyzerDryerController",
         "Su tahliyesi esnasinda hatta basinc olacagindan dikkatli ve yavas aciniz."
      );
   }
   if (h2DrainOpen && now - drainStartTs >= Constants::H2_DRYER_DISCHARGE_TIME) {
      SetDigitalOutputVal(ACT_H2_DRYER_DISCHARGE_VALVE_SV_04, LOW);
      h2DrainOpen = false;
      logMessage(LOG_INFO, F("H2 dryer discharge ended."));
   }

   // O2 dryer: simple on/off based on LS-04
   bool o2Full = (GetDigitalInputVal(sensorO2Dryer) == HIGH);
   if (o2Full) {
      SetDigitalOutputVal(ACT_WATER_FILL_PUMP_PM_01, HIGH);
   } else {
      SetDigitalOutputVal(ACT_WATER_FILL_PUMP_PM_01, LOW);
   }
}