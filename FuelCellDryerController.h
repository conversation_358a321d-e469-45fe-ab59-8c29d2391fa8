#ifndef FUELCELL_DRYER_CONTROLLER_H
#define FUELCELL_DRYER_CONTROLLER_H

/**
 * FuelCellDryerController.h
 * Controls the gas drying systems for fuel cell
 * Manages water removal from O2 gas stream
 */

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

/**
 * FuelCellDryerController class
 * Monitors dryer levels and controls discharge valves and pumps
 * to maintain proper gas dryness
 */
class FuelCellDryerController {
private:
  uint8_t sensorFcO2;
  bool fcO2DryerActive;
  unsigned long lastFcO2DryerToggle;

public:
  FuelCellDryerController();

  /**
   * Initialize dryer controller
   * Sets up input pins and initial states
   */
  void begin();

  /**
   * Main update method - monitors dryer levels and controls discharge
   * Called periodically from the main loop
   */
  void update();

  /**
   * Check if fuel cell O2 dryer is active
   * @return True if dryer discharge is active
   */
  bool isFcO2DryerActive() const { return fcO2DryerActive; }
};

extern FuelCellDryerController fuelcell_dryer_controller;

#endif // FUELCELL_DRYER_CONTROLLER_H
