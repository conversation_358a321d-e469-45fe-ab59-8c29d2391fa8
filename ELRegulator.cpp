#include "ELRegulator.h"
#include "Sensors.h"

// Global ELRegulator instance
ELRegulator elRegulator;

ELRegulator::ELRegulator() : desiredVoltage(0.0f) {
}

void ELRegulator::begin() {
  pinMode(EL_REGULATOR_PWM_PIN, OUTPUT);
  setOutputVoltage(0.0f);  // Initialize to 0V
}

void ELRegulator::setOutputVoltage(float voltage) {
  if(voltage >0){
    SetDigitalOutputVal(ACT_ELECTROLIZER_PSU_RELAY, LOW);    
  }
  else{
    SetDigitalOutputVal(ACT_ELECTROLIZER_PSU_RELAY, HIGH);
  }

  // Constrain voltage to valid range
  voltage = constrain(voltage, 0.0f, Constants::EL_REGULATOR_SUPPLY_MAX_V);
  desiredVoltage = voltage;
  
  // Calculate PWM duty cycle (0-255)
  int duty = int((voltage / Constants::EL_REGULATOR_SUPPLY_MAX_V) * 255.0f + 0.5f);
  
  // Set PWM output
  analogWrite(EL_REGULATOR_PWM_PIN, duty);
}

float ELRegulator::readPinVoltage(uint8_t pin) {
  int raw = analogRead(pin);
  return raw * (Constants::EL_REGULATOR_ADC_REF / Constants::EL_REGULATOR_ADC_MAX);
}

float ELRegulator::readSupplyVoltage() {
  float vPin = readPinVoltage(EL_REGULATOR_VOLT_MON_PIN);
  return vPin * (Constants::EL_REGULATOR_SUPPLY_MAX_V / Constants::EL_REGULATOR_ADC_REF);
}

float ELRegulator::readSupplyCurrent() {
  float vPin = readPinVoltage(EL_REGULATOR_CURR_MON_PIN);
  return vPin * (Constants::EL_REGULATOR_SUPPLY_MAX_I / Constants::EL_REGULATOR_ADC_REF);
}
