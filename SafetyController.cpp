#include "SafetyController.h"
#include "SystemController.h"
#include "ElectrolyzerController.h"
#include "FuelCellController.h"
#include "ELRegulator.h"

// Global SafetyController instance
SafetyController safety_controller;

extern SystemController system_controller;
extern ELRegulator elRegulator;

SafetyController::SafetyController()
   : activeFaults(0),
      lastCheckTs(0),
      lastCommTs(millis())
{}


void SafetyController::update() {
      unsigned long now = millis();
      if (now - lastCheckTs < Constants::CONTROL_LOOP_FREQ_MS) return;
      lastCheckTs = now;

      // Start fresh but keep any self-test failures latched
      uint8_t newF = activeFaults & FAULT_SELFTEST_FAIL;

      // 1) Water pressure (PT-01)
      float pt01 = getFilteredCalibratedValueADS(
            ADC_SENSOR_WATER_PRESS_PT_01, CAL_WATER_PRESS);
      if (pt01 < 0.0f) newF |= FAULT_UNDERPRESSURE;
      else if (pt01 > Constants::WATER_PRESSURE_STOP) newF |= FAULT_OVERPRESSURE;

      // 2) Tank pressures (PT-02 & PT-03)
      float pt02 = getFilteredCalibratedValueADS(
            ADC_SENSOR_H2_TANK_PRESS_PT_02, CAL_H2_TANK_PRESS);
      float pt03 = getFilteredCalibratedValueADS(
            ADC_SENSOR_O2_TANK_PRESS_PT_03, CAL_O2_TANK_PRESS);
      if (pt02 < 0.0f || pt03 < 0.0f) newF |= FAULT_UNDERPRESSURE;
      if (pt02 > Constants::MAX_TANK_PRESSURE_BAR ||
            pt03 > Constants::MAX_TANK_PRESSURE_BAR) newF |= FAULT_OVERPRESSURE;

      // 3) Temperatures TT01–TT04
      auto badTemp = [&](uint8_t sensor, uint8_t cal){
            float t = getFilteredCalibratedValueADS(sensor, cal);
            return (t < 15.0f || t > 55.0f);
      };
      if (badTemp(ADC_SENSOR_WATER_TEMP_TT_01, CAL_WATER_TEMP) ||
            badTemp(ADC_SENSOR_ELEC_TEMP_TT_02, CAL_ELEC_TEMP) ||
            badTemp(ADC_SENSOR_H2_TANK_TEMP_TT_03, CAL_H2_TANK_TEMP) ||
            badTemp(ADC_SENSOR_O2_TANK_TEMP_TT_04, CAL_O2_TANK_TEMP))
      {
            newF |= FAULT_OVERTEMP;
      }

      // 4) Water level (LS-01)
      float lvl01 = getFilteredCalibratedValueADS(
            ADC_SENSOR_WATER_LEVEL_LS_01, CAL_WATER_LEVEL);
      if (lvl01 < Constants::WATER_LEVEL_MIN) newF |= FAULT_LOW_WATER_LEVEL;

      // 5) Comm watchdog
      if (now - lastCommTs > Constants::COMM_WATCHDOG_TIMEOUT_MS)
            newF |= FAULT_COMMS_LOSS;

      activeFaults = newF;
      if (activeFaults) emergency_shutdown();
}

void SafetyController::reportSubsystemFault(FaultCode f) {
   activeFaults |= f;
}

void SafetyController::resetCommWatchdog() {
   lastCommTs = millis();
}

void SafetyController::clearFaults() {
   activeFaults = 0;
}

uint8_t SafetyController::getFaultStatus() const {
   return activeFaults;
}

bool SafetyController::isEmergency() const {
   return (activeFaults != 0) &&
              system_controller.get_mode() == SystemController::MODE_EMERGENCY;
}

void SafetyController::triggerEmergency(){
  emergency_shutdown();
}

void SafetyController::emergency_shutdown() {
      // Hex string of active bits
      String bits = String(activeFaults, HEX);
      logMessage(LOG_ERROR, F("Safety: EMERGENCY shutdown! Faults= "), bits);

      // PRS-7: Automatic shutdown procedure per working procedures
      system_controller.force_mode(SystemController::MODE_EMERGENCY);

      // Shutdown all actuators except CF-01 and CF-02 chillers per PRS-7
      for (int pin = ACTUATOR_PIN_START; pin <= ACTUATOR_PIN_END; ++pin) {
            if (pin == ACT_EL_O2_CHILLER_RELAY_CF_01 ||
                  pin == ACT_FC_O2_CHILLER_RELAY_CF_02)
                  continue;
            SetDigitalOutputVal(pin, LOW);
      }

      // Cut electrolyzer power supply and main input per PRS-7
      SetDigitalOutputVal(ACT_ELECTROLIZER_PSU_RELAY, LOW);
      SetDigitalOutputVal(ACT_MAIN_INPUT_RELAY, LOW);

      // Switch to battery power for chillers and MCU per PRS-7
      SetDigitalOutputVal(ACT_MAIN_BMS_OUT_RELAY, HIGH);

      // Open emergency vent for safety
      SetDigitalOutputVal(ACT_EMERGENCY_VENT, HIGH);

      // Turn off electrolyzer regulator
      elRegulator.setOutputVoltage(0.0f);
}




