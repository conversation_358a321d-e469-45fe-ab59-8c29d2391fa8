#ifndef ELECTROLYZER_DRYER_CONTROLLER_H
#define ELECTROLYZER_DRYER_CONTROLLER_H

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

class ElectrolyzerDryerController {
private:
   uint8_t sensorO2Dryer; // LS-04
   uint8_t sensorH2Dryer; // LS-03
   bool h2DrainOpen;
   unsigned long drainStartTs;

public:
   ElectrolyzerDryerController();

  /**
   * Initialize dryer controller
   * Sets up input pins and initial states
   */
   void begin();
  /**
   * Main update method - monitors dryer levels and controls discharge
   * Called periodically from the main loop
   */
   void update();
};

extern ElectrolyzerDryerController electrolyzer_dryer_controller;

#endif // ELECTROLYZER_DRYER_CONTROLLER_H