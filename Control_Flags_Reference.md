# ElektrolizorProj - Control Flags Reference

## Global Control Flags

The system now includes several global boolean flags that can be controlled via telemetry or menu systems to enable/disable specific functionality.

### 1. IsHeaterEnabled
```cpp
bool IsHeaterEnabled = true; // Default: enabled
```
**Purpose:** Controls whether the electrolyzer heater (EH-01) can operate
**Location:** Defined in `ElektrolizorProj.ino`, declared in `Constants.h`
**Usage:** Used in `ElectrolyzerController.cpp` heater control logic
**Behavior:**
- `true`: Heater operates normally (auto on when TT-01 < 20°C, off when > 55°C or after 1 min)
- `false`: Heater never activates regardless of temperature

### 2. IsVoltageCheckEnabled  
```cpp
bool IsVoltageCheckEnabled = false; // Default: disabled
```
**Purpose:** Controls whether electrolyzer voltage checks are performed during PRS-1 power check
**Location:** Defined in `ElektrolizorProj.ino`, declared in `Constants.h`
**Usage:** Used in `ElectrolyzerController.cpp` PRS1_POWER_CHECK case
**Behavior:**
- `true`: Voltage and current must meet requirements to proceed
- `false`: Power check is bypassed, system proceeds directly to gas check

### 3. IsSerialPrintEnabled
```cpp
bool IsSerialPrintEnabled = false; // Default: disabled
```
**Purpose:** Controls whether DisplayUtils functions print to Serial console
**Location:** Defined in `DisplayUtils.cpp`, declared in `DisplayUtils.h`
**Usage:** Used in all DisplayUtils functions (showWarning, showError, showState)
**Behavior:**
- `true`: Messages printed to Serial console for debugging
- `false`: No serial output (messages still sent to PC via telemetry)

## Usage Examples

### Via Telemetry Commands
These flags can be controlled through telemetry commands from a PC application:

```cpp
// Example telemetry command handler (to be implemented)
case CMD_SET_CONTROL_FLAGS:
    IsHeaterEnabled = (cmd.data[0] != 0);
    IsVoltageCheckEnabled = (cmd.data[1] != 0);
    IsSerialPrintEnabled = (cmd.data[2] != 0);
    sendAck(serialport, cmd.command, true, "Control flags updated");
    break;
```

### Via Menu System
These flags can be controlled through an operator menu interface:

```cpp
// Example menu handler (to be implemented)
void handleMenuSelection(int menuItem, bool value) {
    switch(menuItem) {
        case MENU_HEATER_ENABLE:
            IsHeaterEnabled = value;
            break;
        case MENU_VOLTAGE_CHECK_ENABLE:
            IsVoltageCheckEnabled = value;
            break;
        case MENU_SERIAL_DEBUG_ENABLE:
            IsSerialPrintEnabled = value;
            break;
    }
}
```

## Safety Considerations

### IsHeaterEnabled
- **Safe to disable:** System can operate without heater
- **Impact:** May affect electrolyzer efficiency in cold conditions
- **Recommendation:** Keep enabled unless maintenance required

### IsVoltageCheckEnabled
- **Use with caution:** Bypasses power supply validation
- **Impact:** System may start with inadequate power supply
- **Recommendation:** Only disable for testing or when power supply is known good

### IsSerialPrintEnabled
- **Safe to enable/disable:** No operational impact
- **Impact:** Only affects debugging output
- **Recommendation:** Enable only when debugging is needed

## Communication Integration

### DisplayUtils → Telemetry
All DisplayUtils functions now automatically send messages to PC via telemetry:
- `showWarning()` → `[WARNING] message` sent to PC
- `showError()` → `[ERROR] message` sent to PC  
- `showState()` → `[STATE] code ACTS: json` sent to PC

### Serial Output Control
Serial printing is now optional and controlled by `IsSerialPrintEnabled`:
- When `false`: Clean operation, no serial noise
- When `true`: Full debugging output to serial console

## Implementation Notes

### Thread Safety
All flags are simple boolean variables accessed from main loop only - no threading concerns.

### Persistence
Flags are not automatically saved to EEPROM. To make settings persistent:
1. Add flags to calibration data structure
2. Save/load with existing EEPROM functions
3. Restore values on system startup

### Default Values
All flags have safe default values:
- `IsHeaterEnabled = true` (normal operation)
- `IsVoltageCheckEnabled = false` (bypass for easier testing)
- `IsSerialPrintEnabled = false` (clean operation)

## Future Enhancements

### Additional Control Flags
Consider adding flags for:
- `IsPressureCheckEnabled` - bypass pressure validation
- `IsTemperatureCheckEnabled` - bypass temperature validation  
- `IsLevelCheckEnabled` - bypass water level checks
- `IsTelemetryEnabled` - enable/disable telemetry communication

### Menu Integration
Implement operator menu to control flags without PC connection.

### Status Reporting
Add telemetry command to report current flag states to PC.
